/**
 * Custom Fields v2 Type Definitions
 * 
 * Comprehensive type definitions for the v2 custom field synchronization system.
 * Provides type safety and clear interfaces for all components.
 * 
 * @fileoverview Type definitions for custom fields v2 system
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";

// ============================================================================
// ENUMS
// ============================================================================

/**
 * Field matching strategies for intelligent field pairing
 */
export enum FieldMatchStrategy {
	/** Exact name/label matching */
	EXACT = "exact",
	/** Normalized matching with German chars, spaces, case handling */
	NORMALIZED = "normalized", 
	/** Fuzzy matching with similarity threshold */
	FUZZY = "fuzzy"
}

/**
 * Supported platforms for field synchronization
 */
export enum Platform {
	AP = "ap",
	CC = "cc"
}

/**
 * Field mapping types
 */
export enum MappingType {
	CUSTOM_TO_CUSTOM = "custom_to_custom",
	CUSTOM_TO_STANDARD = "custom_to_standard", 
	STANDARD_TO_CUSTOM = "standard_to_custom"
}

// ============================================================================
// CONFIGURATION INTERFACES
// ============================================================================

/**
 * Configuration for field matching behavior
 */
export interface FieldMatchConfig {
	/** Primary matching strategy to use */
	strategy: FieldMatchStrategy;
	/** Fuzzy matching threshold (0.0 - 1.0) */
	fuzzyThreshold?: number;
	/** Normalize German characters (ä→a, ö→o, ü→u, ß→ss) */
	normalizeGermanChars?: boolean;
	/** Ignore case differences */
	ignoreCase?: boolean;
	/** Ignore spaces and underscores */
	ignoreSpaces?: boolean;
}

/**
 * Synchronization options for field operations
 */
export interface SyncOptions {
	/** Request ID for correlation */
	requestId: string;
	/** Create missing fields if no match found */
	createMissingFields?: boolean;
	/** Include standard field mappings */
	includeStandardFields?: boolean;
	/** Logging level */
	logLevel?: "DEBUG" | "INFO" | "WARN" | "ERROR";
	/** Dry run mode (no actual changes) */
	dryRun?: boolean;
}

// ============================================================================
// FIELD MAPPING INTERFACES
// ============================================================================

/**
 * Field mapping between AP and CC platforms
 */
export interface FieldMapping {
	/** Unique mapping identifier */
	id: string;
	/** AutoPatient field ID */
	apFieldId: string;
	/** CliniCore field ID */
	ccFieldId: string;
	/** AutoPatient field name */
	apFieldName: string;
	/** CliniCore field name/label */
	ccFieldName: string;
	/** AutoPatient field type */
	apFieldType: string;
	/** CliniCore field type */
	ccFieldType: string;
	/** Matching strategy used */
	matchStrategy: FieldMatchStrategy;
	/** Confidence score (0.0 - 1.0) */
	confidence: number;
	/** Is this a standard field mapping */
	isStandardField: boolean;
	/** Mapping type */
	mappingType?: MappingType;
	/** Is mapping active */
	isActive: boolean;
}

/**
 * Standard field mapping configuration
 */
export interface StandardFieldMapping {
	/** Source field name */
	sourceField: string;
	/** Target field name */
	targetField: string;
	/** Source platform */
	sourcePlatform: Platform;
	/** Target platform */
	targetPlatform: Platform;
	/** Optional notes */
	notes?: string;
}

// ============================================================================
// SYNCHRONIZATION RESULT INTERFACES
// ============================================================================

/**
 * Result of field definition synchronization
 */
export interface FieldSyncResult {
	/** Overall success status */
	success: boolean;
	/** Successfully matched fields */
	matchedFields: FieldMapping[];
	/** Newly created fields */
	createdFields: FieldMapping[];
	/** Skipped fields (incompatible, etc.) */
	skippedFields: Array<{
		fieldName: string;
		reason: string;
		platform: Platform;
	}>;
	/** Failed field operations */
	failedFields: Array<{
		fieldName: string;
		error: string;
		platform: Platform;
	}>;
	/** Processing time in milliseconds */
	processingTimeMs: number;
	/** Warning messages */
	warnings: string[];
}

/**
 * Result of patient value synchronization
 */
export interface ValueSyncResult {
	/** Overall success status */
	success: boolean;
	/** Patient ID */
	patientId: string;
	/** Number of fields processed */
	processedFields: number;
	/** Number of successful updates */
	successfulUpdates: number;
	/** Number of skipped fields */
	skippedFields: number;
	/** Number of failed updates */
	failedUpdates: number;
	/** Processing time in milliseconds */
	processingTimeMs: number;
	/** Error messages */
	errors: string[];
	/** Warning messages */
	warnings: string[];
}

/**
 * Context for value synchronization operations
 */
export interface ValueSyncContext {
	/** Patient ID */
	patientId: string;
	/** Source platform */
	platform: Platform;
	/** Field mappings to use */
	fieldMappings: FieldMapping[];
	/** Standard field mappings */
	standardMappings: StandardFieldMapping[];
	/** Request ID */
	requestId: string;
	/** Dry run mode */
	dryRun: boolean;
}

/**
 * Summary of synchronization operations
 */
export interface SyncSummary {
	/** Patient ID */
	patientId: string;
	/** Number of fields processed */
	processedFields: number;
	/** Number of successful updates */
	successfulUpdates: number;
	/** Number of skipped fields */
	skippedFields: number;
	/** Number of failed updates */
	failedUpdates: number;
	/** Processing time in milliseconds */
	processingTimeMs: number;
	/** Error messages */
	errors: string[];
	/** Warning messages */
	warnings: string[];
}

// ============================================================================
// FIELD COMPATIBILITY INTERFACES
// ============================================================================

/**
 * Field compatibility check result
 */
export interface CompatibilityResult {
	/** Are fields compatible */
	compatible: boolean;
	/** Confidence score */
	confidence: number;
	/** Reason for compatibility/incompatibility */
	reason: string;
	/** Required conversion steps */
	conversionSteps?: string[];
}

/**
 * Value conversion result
 */
export interface ConversionResult {
	/** Conversion success status */
	success: boolean;
	/** Converted value */
	value: unknown;
	/** Original value for reference */
	originalValue: unknown;
	/** Conversion type used */
	conversionType: "textbox_list_object" | "pipe_separated_text" | "direct_mapping" | "boolean_conversion";
	/** Conversion warnings */
	warnings: string[];
	/** Conversion errors */
	errors: string[];
}

// ============================================================================
// PATIENT DATA INTERFACES
// ============================================================================

/**
 * Patient data for synchronization
 */
export interface PatientData {
	/** Patient ID */
	id: string;
	/** Platform this data comes from */
	platform: Platform;
	/** Standard fields */
	standardFields: Record<string, unknown>;
	/** Custom fields */
	customFields: Record<string, unknown>;
	/** Raw platform-specific data */
	rawData: unknown;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export type {
	APGetCustomFieldType,
	GetCCCustomField
} from "@type";
